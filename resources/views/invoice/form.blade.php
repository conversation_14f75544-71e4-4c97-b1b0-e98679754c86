@extends('layouts.contentNavbarLayout')

@section('title', isset($invoice) ? 'Edit Invoice' : 'Buat Invoice')

@section('page-style')
<style>
    .invoice-form {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
        padding: 2rem;
    }

    .form-section {
        background: #f8f9fa;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .form-section-title {
        color: #344767;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1.25rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-section-title i {
        font-size: 1.25rem;
        color: #4f46e5;
    }

    .form-floating {
        position: relative;
        margin-bottom: 1.25rem;
    }

    .form-floating > label {
        position: absolute;
        top: 0;
        left: 0;
        padding: 1rem;
        pointer-events: none;
        transform-origin: 0 0;
        transition: opacity .1s ease-in-out,transform .1s ease-in-out;
        color: #6c757d;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        transform: scale(.85) translateY(-.5rem) translateX(.15rem);
        background: white;
        padding: 0 .5rem;
    }

    .form-floating > .form-control {
        padding: 1rem;
        height: calc(3.5rem + 2px);
    }

    .select2-container--bootstrap-5 .select2-selection {
        min-height: calc(3.5rem + 2px);
        padding: 1rem;
        font-size: 1rem;
        border: 1px solid #dee2e6;
        border-radius: .375rem;
    }

    .select2-container--bootstrap-5.select2-container--focus .select2-selection {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-submit {
        padding: 0.75rem 2rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        border: none;
        color: white;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(79, 70, 229, 0.25);
    }

    .btn-cancel {
        padding: 0.75rem 2rem;
        font-weight: 500;
        letter-spacing: 0.5px;
        background: white;
        border: 1px solid #dee2e6;
        color: #6c757d;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .btn-cancel:hover {
        background: #f8f9fa;
        border-color: #667eea;
        color: #667eea;
    }

    .total-section {
        background: #4f46e5;
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-top: 1.5rem;
    }

    .total-amount {
        font-size: 2rem;
        font-weight: 700;
        text-align: right;
    }

    @media (max-width: 768px) {
        .invoice-form {
            padding: 1rem;
        }

        .form-section {
            padding: 1rem;
        }

        .btn-submit, .btn-cancel {
            width: 100%;
            margin-bottom: 0.5rem;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <form action="{{ isset($invoice) ? route('invoice.update', $invoice->id) : route('invoice.store') }}" 
          method="POST" 
          class="invoice-form needs-validation" 
          novalidate>
        @csrf
        @if(isset($invoice))
            @method('PUT')
        @endif

        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4 class="mb-0 text-dark">
                        {{ isset($invoice) ? 'Edit Invoice' : 'Buat Invoice Baru' }}
                    </h4>
                    <div class="d-flex gap-2">
                        <a href="{{ route('invoice.index') }}" class="btn btn-cancel">
                            <i class="bx bx-arrow-back me-2"></i>Kembali
                        </a>
                        <button type="submit" class="btn btn-submit">
                            <i class="bx bx-save me-2"></i>{{ isset($invoice) ? 'Update' : 'Simpan' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <!-- Customer Information -->
                <div class="form-section">
                    <h5 class="form-section-title">
                        <i class="bx bx-user-circle"></i>
                        Informasi Pelanggan
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select select2" 
                                        name="customer_id" 
                                        required 
                                        data-placeholder="Pilih pelanggan">
                                    <option value="">Pilih pelanggan</option>
                                    @foreach($customers as $customer)
                                        <option value="{{ $customer->id }}" 
                                            {{ (old('customer_id', $invoice->customer_id ?? '') == $customer->id) ? 'selected' : '' }}>
                                            {{ $customer->nama_customer }}
                                        </option>
                                    @endforeach
                                </select>
                                <label>Pelanggan</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" 
                                       class="form-control" 
                                       name="no_invoice" 
                                       value="{{ old('no_invoice', $invoice->no_invoice ?? $nextInvoiceNumber) }}" 
                                       readonly>
                                <label>Nomor Invoice</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Information -->
                <div class="form-section">
                    <h5 class="form-section-title">
                        <i class="bx bx-package"></i>
                        Informasi Paket
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select select2" 
                                        name="paket_id" 
                                        required 
                                        data-placeholder="Pilih paket">
                                    <option value="">Pilih paket</option>
                                    @foreach($pakets as $paket)
                                        <option value="{{ $paket->id }}" 
                                            data-harga="{{ $paket->harga }}"
                                            {{ (old('paket_id', $invoice->paket_id ?? '') == $paket->id) ? 'selected' : '' }}>
                                            {{ $paket->nama_paket }} - Rp {{ number_format($paket->harga, 0, ',', '.') }}
                                        </option>
                                    @endforeach
                                </select>
                                <label>Paket Layanan</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" name="bulan" required>
                                    <option value="">Pilih bulan tagihan</option>
                                    @foreach($months as $key => $month)
                                        <option value="{{ $key }}" 
                                            {{ (old('bulan', $invoice->bulan ?? '') == $key) ? 'selected' : '' }}>
                                            {{ $month }}
                                        </option>
                                    @endforeach
                                </select>
                                <label>Bulan Tagihan</label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="form-section">
                    <h5 class="form-section-title">
                        <i class="bx bx-credit-card"></i>
                        Informasi Pembayaran
                    </h5>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="date" 
                                       class="form-control" 
                                       name="tanggal_tagihan" 
                                       value="{{ old('tanggal_tagihan', $invoice->tanggal_tagihan ?? date('Y-m-d')) }}" 
                                       required>
                                <label>Tanggal Tagihan</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="date" 
                                       class="form-control" 
                                       name="jatuh_tempo" 
                                       value="{{ old('jatuh_tempo', $invoice->jatuh_tempo ?? date('Y-m-d', strtotime('+7 days'))) }}" 
                                       required>
                                <label>Jatuh Tempo</label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-floating">
                                <select class="form-select" name="metode_id" required>
                                    <option value="">Pilih metode pembayaran</option>
                                    @foreach($metodes as $metode)
                                        <option value="{{ $metode->id }}" 
                                            {{ (old('metode_id', $invoice->metode_id ?? '') == $metode->id) ? 'selected' : '' }}>
                                            {{ $metode->nama_metode }}
                                        </option>
                                    @endforeach
                                </select>
                                <label>Metode Pembayaran</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Summary Card -->
                <div class="form-section">
                    <h5 class="form-section-title">
                        <i class="bx bx-receipt"></i>
                        Ringkasan Invoice
                    </h5>
                    <div class="mb-3">
                        <label class="form-label text-muted">Status</label>
                        <select class="form-select" name="status_id" required>
                            @foreach($statuses as $status)
                                <option value="{{ $status->id }}" 
                                    {{ (old('status_id', $invoice->status_id ?? '') == $status->id) ? 'selected' : '' }}>
                                    {{ $status->nama_status }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Catatan</label>
                        <textarea class="form-control" 
                                  name="keterangan" 
                                  rows="3" 
                                  placeholder="Tambahkan catatan jika diperlukan">{{ old('keterangan', $invoice->keterangan ?? '') }}</textarea>
                    </div>
                    <div class="total-section">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>Subtotal</span>
                            <span class="subtotal">Rp 0</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Total</h5>
                            <div class="total-amount">Rp <span id="totalAmount">0</span></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
@endsection

@section('page-script')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Select2
    $('.select2').select2({
        theme: 'bootstrap-5',
        width: '100%'
    });

    // Calculate total when package changes
    $('select[name="paket_id"]').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const harga = selectedOption.data('harga') || 0;
        updateTotal(harga);
    });

    // Format number to currency
    function formatCurrency(number) {
        return new Intl.NumberFormat('id-ID').format(number);
    }

    // Update total display
    function updateTotal(amount) {
        $('.subtotal').text('Rp ' + formatCurrency(amount));
        $('#totalAmount').text(formatCurrency(amount));
    }

    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Set initial total if editing
    const initialPackage = $('select[name="paket_id"]').find('option:selected');
    if (initialPackage.length) {
        updateTotal(initialPackage.data('harga') || 0);
    }
});
</script>
@endsection